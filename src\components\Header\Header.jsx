import React, { useState, useCallback, memo } from "react";
import { Link } from "react-router-dom";
import "./Header.css";
import { SITE_CONFIG, ASSETS } from "../../config/constants";

const Header = memo(() => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);

  const toggleMenu = useCallback(() => {
    setIsMenuOpen((prev) => !prev);
    document.body.classList.toggle("menu-open");
  }, []);

  const closeMenu = useCallback(() => {
    setIsMenuOpen(false);
    document.body.classList.remove("menu-open");
  }, []);

  return (
    <>
      <div className={`c-menu__overlay ${isMenuOpen ? "active" : ""}`}></div>
      <div className={`c-menu ${isMenuOpen ? "is-open" : ""}`}>
        <button
          className="c-menu__close-button u-btn-reset"
          onClick={toggleMenu}
          aria-label="Close menu"
        >
          &times; {/* Or use an icon here */}
        </button>
        <div className="c-menu__wrap splide">
          <nav className="c-navigation splide__track">
            <ul className="c-navigation__list splide__list">
              {SITE_CONFIG.navigation.map((item) => (
                <li
                  key={item.path}
                  className="menu-item splide__slide is-active is-visible"
                >
                  <Link to={item.path} onClick={closeMenu}>
                    {item.label}
                  </Link>
                </li>
              ))}
            </ul>
          </nav>
        </div>
      </div>

      <div className="c-header__progress" data-aos="fade-in">
        <div></div>
      </div>
      <header className="c-header c-header--static">
        <div className="c-header__body" data-aos="fade-in">
          <div className="c-header__body-col">
            <div className="c-logo">
              <Link to="/" title={`${SITE_CONFIG.name} home page`}>
                <img
                  src={ASSETS.logos.black}
                  alt={`${SITE_CONFIG.name} Logo`}
                  width="121"
                  height="28"
                />
              </Link>
            </div>
          </div>

          <div className="c-header__body-col c-header__action">
            <div className="c-burger">
              <button
                className="c-burger__action u-btn-reset"
                id="open-menu"
                onClick={toggleMenu}
                aria-label="Open menu"
                aria-expanded={isMenuOpen}
              >
                <div className="c-burger__wrap">
                  <span></span>
                  <span></span>
                </div>
              </button>
            </div>
          </div>
        </div>
      </header>
    </>
  );
});

Header.displayName = "Header";

export default Header;
