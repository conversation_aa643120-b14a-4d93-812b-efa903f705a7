/* Header styles - Desktop first, then mobile overrides */
.c-header {
  position: fixed;
  top: 2rem;
  right: 2rem;
  left: auto;
  width: auto;
  min-width: auto;
  max-width: 700px;
  z-index: 1000;
  background-color: rgba(255, 255, 255, 0.25); /* semi-transparent white */
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.08);
  -webkit-backdrop-filter: blur(18px);
  backdrop-filter: blur(18px);
  border-radius: 2rem;
  border-bottom: none;
  border: 1px solid rgba(0, 0, 0, 0.04);
  padding: 0.5rem 3rem;
  display: flex;
  align-items: center;
}

.c-header__body {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.25rem 2rem;
  width: auto;
}

.c-logo {
  display: block;
}

.c-logo img {
  height: 28px;
  width: auto;
  max-width: 121px;
}

.c-burger__action {
  display: flex;
  align-items: center;
  cursor: pointer;
  background: none;
  border: none;
}

.c-burger__wrap {
  display: flex;
  flex-direction: column;
}

.c-burger__wrap span {
  display: block;
  width: 30px;
  height: 2px;
  background-color: #000;
  margin: 3px 0;
  transition: all 0.3s ease;
}

/* Menu overlay */
.c-menu__overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1020;
  opacity: 0;
  transition: opacity 0.3s ease;
  pointer-events: none;
}

.c-menu__overlay.active {
  height: 100%;
  opacity: 1;
  pointer-events: all;
}

.c-menu__close-button {
  position: absolute;
  top: 1rem;
  right: 1rem;
  font-size: 2rem;
  color: #fff;
  background: none;
  border: none;
  cursor: pointer;
  z-index: 1031; /* Ensure it's above other menu content */
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: background-color 0.3s ease;
}

.c-menu__close-button:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.c-menu__close-button:active {
  transform: scale(0.95);
}

/* Menu styles */
.c-menu {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: #ff4338;
  z-index: 1030;
  transform: translateY(-100%);
  transition: transform 0.5s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.c-menu.is-open {
  transform: translateY(0);
}

.c-navigation__list {
  list-style: none;
  padding: 0;
  margin: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.c-navigation__list li {
  margin: 1rem 0;
}

.c-navigation__list li a {
  color: #fff;
  font-size: 2.5rem;
  text-decoration: none;
  font-weight: 600;
  transition: all 0.3s ease;
}

.c-navigation__list li a:hover {
  opacity: 0.8;
}

/* Progress bar */
.c-header__progress {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 4px;
  z-index: 1001;
}

.c-header__progress div {
  height: 100%;
  background-color: #ff4338;
  width: 0;
  transition: width 0.3s ease;
}

/* Header static */
.c-header--static {
  position: fixed;
  background-color: transparent;
  transition: background-color 0.3s ease;
}

body.menu-open {
  overflow: hidden;
}

/* Mobile-first responsive design */
@media (max-width: 768px) {
  .c-header {
    top: 1rem;
    right: 1rem;
    left: 1rem;
    max-width: calc(100vw - 2rem);
    padding: 0.75rem 1.5rem;
    border-radius: clamp(1rem, 4vw, 1.5rem);
  }

  .c-header__body {
    padding: 1rem 1.5rem;
  }

  .c-logo img {
    height: clamp(24px, 5vw, 28px);
    max-width: clamp(100px, 20vw, 121px);
  }

  .c-burger__wrap span {
    width: clamp(24px, 5vw, 30px);
    height: 2px;
    margin: 3px 0;
  }

  .c-navigation__list li a {
    font-size: clamp(2rem, 6vw, 2.5rem);
  }

  .c-menu__close-button {
    top: 1rem;
    right: 1rem;
    width: clamp(36px, 8vw, 40px);
    height: clamp(36px, 8vw, 40px);
    font-size: clamp(1.5rem, 4vw, 2rem);
  }
}

/* Desktop responsive improvements */
@media (min-width: 769px) {
  .c-navigation__list {
    flex-direction: row;
  }

  .c-navigation__list li {
    margin: 0 1.5rem;
  }
}
