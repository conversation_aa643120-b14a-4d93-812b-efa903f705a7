/* Header styles */
.c-header {
  position: fixed;
  top: 1rem;
  right: 1rem;
  left: 1rem;
  width: auto;
  max-width: calc(100vw - 2rem);
  z-index: 1000;
  background-color: rgba(255, 255, 255, 0.25); /* semi-transparent white */
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.08);
  -webkit-backdrop-filter: blur(18px);
  backdrop-filter: blur(18px);
  border-radius: clamp(1rem, 4vw, 2rem);
  border-bottom: none;
  border: 1px solid rgba(0, 0, 0, 0.04);
  padding: clamp(0.5rem, 2vw, 1rem) clamp(1rem, 4vw, 3rem);
  display: flex;
  align-items: center;
}

.c-header__body {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: clamp(0.75rem, 2vw, 1.25rem) clamp(1rem, 3vw, 2rem);
  max-width: min(1200px, 100%);
  margin: 0 auto;
  width: 100%;
}

.c-logo {
  display: block;
}

.c-logo img {
  height: clamp(24px, 4vw, 32px);
  width: auto;
  max-width: clamp(100px, 15vw, 140px);
}

.c-burger__action {
  display: flex;
  align-items: center;
  cursor: pointer;
  background: none;
  border: none;
}

.c-burger__wrap {
  display: flex;
  flex-direction: column;
}

.c-burger__wrap span {
  display: block;
  width: clamp(24px, 4vw, 32px);
  height: clamp(2px, 0.3vw, 3px);
  background-color: #000;
  margin: clamp(2px, 0.5vw, 4px) 0;
  transition: all 0.3s ease;
}

/* Menu overlay */
.c-menu__overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1020;
  opacity: 0;
  transition: opacity 0.3s ease;
  pointer-events: none;
}

.c-menu__overlay.active {
  height: 100%;
  opacity: 1;
  pointer-events: all;
}

.c-menu__close-button {
  position: absolute;
  top: clamp(1rem, 3vw, 2rem);
  right: clamp(1rem, 3vw, 2rem);
  font-size: clamp(1.5rem, 4vw, 2.5rem);
  color: #fff;
  background: none;
  border: none;
  cursor: pointer;
  z-index: 1031; /* Ensure it's above other menu content */
  width: clamp(36px, 6vw, 48px);
  height: clamp(36px, 6vw, 48px);
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: background-color 0.3s ease;
}

.c-menu__close-button:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.c-menu__close-button:active {
  transform: scale(0.95);
}

/* Menu styles */
.c-menu {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: #ff4338;
  z-index: 1030;
  transform: translateY(-100%);
  transition: transform 0.5s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.c-menu.is-open {
  transform: translateY(0);
}

.c-navigation__list {
  list-style: none;
  padding: 0;
  margin: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.c-navigation__list li {
  margin: clamp(0.75rem, 2vw, 1.5rem) 0;
}

.c-navigation__list li a {
  color: #fff;
  font-size: clamp(1.8rem, 5vw, 3rem);
  text-decoration: none;
  font-weight: 600;
  transition: all 0.3s ease;
}

.c-navigation__list li a:hover {
  opacity: 0.8;
}

/* Progress bar */
.c-header__progress {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 4px;
  z-index: 1001;
}

.c-header__progress div {
  height: 100%;
  background-color: #ff4338;
  width: 0;
  transition: width 0.3s ease;
}

/* Header static */
.c-header--static {
  position: fixed;
  background-color: transparent;
  transition: background-color 0.3s ease;
}

body.menu-open {
  overflow: hidden;
}

/* Mobile-first responsive design */
@media (max-width: 480px) {
  .c-header {
    top: 0.5rem;
    right: 0.5rem;
    left: 0.5rem;
    padding: 0.5rem 1rem;
    border-radius: 1rem;
  }

  .c-header__body {
    padding: 0.75rem 1rem;
  }

  .c-logo img {
    height: 22px;
    max-width: 90px;
  }

  .c-burger__wrap span {
    width: 22px;
    height: 2px;
    margin: 2px 0;
  }

  .c-navigation__list li a {
    font-size: 1.8rem;
  }

  .c-menu__close-button {
    top: 1rem;
    right: 1rem;
    width: 36px;
    height: 36px;
    font-size: 1.5rem;
  }
}

@media (min-width: 481px) and (max-width: 768px) {
  .c-header {
    top: 0.75rem;
    right: 0.75rem;
    left: 0.75rem;
    padding: 0.75rem 1.5rem;
  }

  .c-header__body {
    padding: 1rem 1.5rem;
  }

  .c-navigation__list li a {
    font-size: 2.2rem;
  }
}

@media (min-width: 769px) and (max-width: 1024px) {
  .c-header {
    top: 1.25rem;
    right: 1.25rem;
    left: auto;
    max-width: 600px;
  }

  .c-navigation__list {
    flex-direction: row;
  }

  .c-navigation__list li {
    margin: 0 clamp(1rem, 2vw, 1.5rem);
  }

  .c-navigation__list li a {
    font-size: 2.5rem;
  }
}

@media (min-width: 1025px) {
  .c-header {
    top: 1.5rem;
    right: 1.5rem;
    left: auto;
    max-width: 700px;
    padding: 0.75rem 2.5rem;
  }

  .c-header__body {
    padding: 1.25rem 2rem;
  }

  .c-navigation__list {
    flex-direction: row;
  }

  .c-navigation__list li {
    margin: 0 1.5rem;
  }

  .c-navigation__list li a {
    font-size: 2.8rem;
  }
}

@media (min-width: 1400px) {
  .c-header {
    top: 2rem;
    right: 2rem;
    max-width: 750px;
    padding: 1rem 3rem;
  }

  .c-logo img {
    height: 32px;
    max-width: 140px;
  }

  .c-navigation__list li a {
    font-size: 3rem;
  }
}
