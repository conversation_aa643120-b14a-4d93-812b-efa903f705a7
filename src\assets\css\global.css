/* Global styles */
html {
  scroll-behavior: smooth;
}

body {
  overflow-x: hidden;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  padding-top: 80px; /* Account for fixed header height */
}

/* Responsive body padding adjustments */
@media (max-width: 768px) {
  body {
    padding-top: 70px; /* Smaller padding on mobile */
  }
}

@media (min-width: 1200px) {
  body {
    padding-top: 85px; /* Slightly more padding on larger screens */
  }
}

/* Typography */
h1,
h2,
h3,
h4,
h5,
h6 {
  margin-top: 0;
  font-weight: 600;
}

p {
  margin-bottom: 1rem;
}

em {
  font-style: italic;
  position: relative;
  display: inline-block;
}

/* Animation classes */
[data-aos] {
  opacity: 0;
  transform: translateY(20px);
  transition: opacity 0.6s ease, transform 0.6s ease;
}

[data-aos].aos-animate {
  opacity: 1;
  transform: translateY(0);
}

/* Layout utility classes */
.u-push-right {
  margin-left: auto;
}

.u-pull-up-l {
  margin-top: -3rem;
}

.u-over-red {
  color: var(--primary-color);
}

.u-fade-el {
  opacity: 0.7;
  transition: opacity 0.3s ease;
}

.u-fade-el:hover {
  opacity: 1;
}

/* Cards and components */
.c-project-card {
  position: relative;
  overflow: hidden;
  border-radius: 4px;
  height: 400px;
  margin-bottom: 2rem;
}

.c-project-card__action {
  display: block;
  height: 100%;
  width: 100%;
  text-decoration: none;
  color: white;
}

.c-project-card__image {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 0;
}

.c-project-card__image::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    180deg,
    rgba(0, 0, 0, 0.4) 0%,
    rgba(0, 0, 0, 0) 58.94%
  );
  z-index: 1;
}

.c-project-card__image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.c-project-card__content {
  position: relative;
  z-index: 2;
  padding: 1.5rem;
}

.c-project-card__meta {
  font-size: 0.875rem;
  opacity: 0.8;
  margin-bottom: 0.5rem;
}

.c-project-card__heading {
  font-size: 1.5rem;
  margin-bottom: 0;
}

/* Careers cards */
.c-careers-card {
  padding: 1.5rem;
  background-color: var(--background-gray);
  border-radius: 4px;
  margin-bottom: 1.5rem;
  height: 100%;
}

.c-careers-card__action {
  display: block;
  color: inherit;
  text-decoration: none;
  height: 100%;
}

.u-bullet-heading {
  font-size: 0.875rem;
  font-weight: 600;
  margin-bottom: 0.75rem;
}

.u-bullet-heading.top,
.u-bullet-heading.dark {
  color: var(--primary-color);
}

.c-link {
  display: inline-flex;
  align-items: center;
  font-weight: 600;
  text-decoration: none;
  color: inherit;
}

.c-link svg {
  margin-right: 0.5rem;
}

.c-link.dark {
  color: var(--text-color);
}

/* CTA Cards */
.c-cta-card__wrap {
  margin-bottom: 3rem;
}

.c-cta-card {
  position: relative;
  overflow: hidden;
  border-radius: 4px;
  height: 500px;
}

.c-cta-card.red::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(255, 67, 56, 0.5);
  z-index: 1;
}

.c-cta-card__action {
  display: block;
  height: 100%;
  width: 100%;
  text-decoration: none;
  color: white;
}

.c-cta-card__image {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.c-cta-card__image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.c-cta-card__content {
  position: absolute;
  bottom: 0;
  left: 0;
  padding: 2rem;
  z-index: 2;
}

/* Intro section */
.c-intro {
  height: 100vh;
  position: relative;
  overflow: hidden;
  background-color: var(--primary-color);
}

.c-intro__motion {
  height: 100%;
  width: 100%;
}

.c-intro__motion video {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.c-intro__outro {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: white;
  font-size: 2rem;
  font-weight: bold;
  text-align: center;
}

.c-intro__cue {
  position: absolute;
  bottom: 2rem;
  left: 50%;
  transform: translateX(-50%);
  color: white;
  display: flex;
  flex-direction: column;
  align-items: center;
  animation: bounce 1.5s infinite;
}

@keyframes bounce {
  0%,
  20%,
  50%,
  80%,
  100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10px);
  }
  60% {
    transform: translateY(-5px);
  }
}

/* Half bg section */
.u-half-bg {
  position: relative;
}

.u-half-bg::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 50%;
  background-color: var(--background-gray);
  z-index: -1;
}

/* Intro text styles */
.c-intro-text {
  font-size: 1.25rem;
  line-height: 1.6;
}

.c-intro-text em {
  font-weight: 600;
  color: var(--primary-color);
}

/* Offset Grid */
.c-offset-grid {
  position: relative;
}

/* Responsive utility classes */
@media (min-width: 768px) {
  .c-intro-text {
    font-size: 1.5rem;
  }

  .c-project-card {
    height: 500px;
  }
}
